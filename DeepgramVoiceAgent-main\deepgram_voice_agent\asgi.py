"""
ASGI config for deepgram_voice_agent project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import voice_agent.routing

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "deepgram_voice_agent.settings")

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            voice_agent.routing.websocket_urlpatterns
        )
    ),
})
