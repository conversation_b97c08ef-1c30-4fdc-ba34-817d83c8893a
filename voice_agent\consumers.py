"""
Django Channels WebSocket consumer for Deepgram Voice Agent.
Converts the original main.py WebSocket logic to Django Channels while preserving
all functionality and maintaining compatibility with Twilio integration.
"""

import asyncio
import base64
import json
import websockets
import os
from channels.generic.websocket import AsyncWebsocketConsumer
from django.conf import settings
from pharmacy.services import FUNCTION_MAP


class TwilioVoiceConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer that handles Twilio voice connections and integrates
    with Deepgram Voice Agent API. Maintains the same functionality as the
    original main.py implementation.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.audio_queue = None
        self.streamsid_queue = None
        self.sts_ws = None
        
    async def connect(self):
        """Accept the WebSocket connection from Twilio."""
        await self.accept()
        print("Twilio WebSocket connected")
        
        # Initialize queues for audio and stream ID communication
        self.audio_queue = asyncio.Queue()
        self.streamsid_queue = asyncio.Queue()
        
        # Start the voice agent handler
        await self.start_voice_agent()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        print(f"Twilio WebSocket disconnected with code: {close_code}")
        if self.sts_ws:
            await self.sts_ws.close()
    
    async def receive(self, text_data):
        """
        Handle incoming messages from Twilio.
        This replaces the twilio_receiver function from main.py.
        """
        try:
            data = json.loads(text_data)
            event = data["event"]
            
            if event == "start":
                print("Got our streamsid")
                start = data["start"]
                streamsid = start["streamSid"]
                self.streamsid_queue.put_nowait(streamsid)
            elif event == "connected":
                pass  # Continue processing
            elif event == "media":
                await self.handle_media_message(data)
            elif event == "stop":
                await self.disconnect(1000)
                
        except Exception as e:
            print(f"Error processing Twilio message: {e}")
    
    async def handle_media_message(self, data):
        """
        Handle media messages from Twilio and buffer audio.
        Maintains the same buffering logic as the original implementation.
        """
        # Buffer size: 20 * 160 bytes = 3200 bytes (0.4 seconds of audio)
        BUFFER_SIZE = 20 * 160
        
        if not hasattr(self, 'inbuffer'):
            self.inbuffer = bytearray(b"")
            
        media = data["media"]
        chunk = base64.b64decode(media["payload"])
        
        if media["track"] == "inbound":
            self.inbuffer.extend(chunk)
            
        # Send buffered audio when ready
        while len(self.inbuffer) >= BUFFER_SIZE:
            audio_chunk = self.inbuffer[:BUFFER_SIZE]
            self.audio_queue.put_nowait(audio_chunk)
            self.inbuffer = self.inbuffer[BUFFER_SIZE:]
    
    async def start_voice_agent(self):
        """
        Start the voice agent with Deepgram connection.
        This replaces the twilio_handler function from main.py.
        """
        try:
            # Establish connection to Deepgram
            self.sts_ws = await self.sts_connect()
            
            # Send configuration to Deepgram
            config_message = self.load_config()
            await self.sts_ws.send(json.dumps(config_message))
            
            # Start concurrent tasks for audio processing
            await asyncio.gather(
                self.sts_sender(),
                self.sts_receiver(),
                return_exceptions=True
            )
            
        except Exception as e:
            print(f"Error in voice agent: {e}")
            await self.close()
    
    async def sts_connect(self):
        """
        Establish WebSocket connection to Deepgram's agent service.
        Maintains the same connection logic as the original implementation.
        """
        api_key = os.getenv("DEEPGRAM_API_KEY")
        if not api_key:
            raise Exception("DEEPGRAM_API_KEY not found")
        
        sts_ws = await websockets.connect(
            "wss://agent.deepgram.com/v1/agent/converse",
            subprotocols=["token", api_key]
        )
        return sts_ws
    
    def load_config(self):
        """
        Load configuration for Deepgram Voice Agent.
        Uses Django settings instead of config.json.
        """
        return getattr(settings, 'DEEPGRAM_VOICE_AGENT_CONFIG', {
            "type": "Settings",
            "audio": {
                "input": {
                    "encoding": "mulaw",
                    "sample_rate": 8000
                },
                "output": {
                    "encoding": "mulaw",
                    "sample_rate": 8000,
                    "container": "none"
                }
            },
            "agent": {
                "language": "en",
                "listen": {
                    "provider": {
                        "type": "deepgram",
                        "model": "nova-3",
                        "keyterms": ["hello", "goodbye"]
                    }
                },
                "think": {
                    "provider": {
                        "type": "open_ai",
                        "model": "gpt-4o-mini",
                        "temperature": 0.7
                    },
                    "prompt": "You are a professional pharmacy assistant. You can: 1) Get drug info with get_drug_info, 2) Place orders with place_order, 3) Look up orders with lookup_order. IMPORTANT: Always ask users to spell out their full name clearly when placing orders. Confirm all order details before processing - including customer name, drug name, and quantity. Be thorough and professional in collecting information. If a user provides a name that's unclear, ask them to spell it out letter by letter. Always confirm the complete order details before finalizing any transaction.",
                    "functions": [
                        {
                            "name": "get_drug_info",
                            "description": "Get detailed information about a specific drug. Use this function when: A customer asks about drug information, side effects, or pricing. A customer wants to know what a medication does. A customer asks questions like 'What is aspirin?' or 'Tell me about ibuprofen'. A customer wants to know drug prices or descriptions. Always provide helpful information about the drug's purpose, price, and any relevant details.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "drug_name": {
                                        "type": "string",
                                        "description": "Name of the drug to look up. Convert to lowercase for matching. Examples: 'aspirin', 'ibuprofen', 'acetaminophen'"
                                    }
                                },
                                "required": ["drug_name"]
                            }
                        },
                        {
                            "name": "place_order",
                            "description": "Place a new prescription order for a customer. Use this function when: A customer wants to order medication. A customer asks to refill a prescription. A customer says 'I need to order' or 'Can I get some'. Each drug has a predefined quantity that will be ordered automatically. Before placing an order: Verify the drug exists using get_drug_info. Get the customer's name for the order. Always confirm the order details before processing.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "customer_name": {
                                        "type": "string",
                                        "description": "Customer's full name for the order. Use as provided by the customer."
                                    },
                                    "drug_name": {
                                        "type": "string",
                                        "description": "Name of the drug to order. Convert to lowercase for matching. Must be a valid drug in our system. Examples: 'aspirin', 'metformin', 'lisinopril', 'atorvastatin', 'omeprazole'"
                                    }
                                },
                                "required": ["customer_name", "drug_name"]
                            }
                        },
                        {
                            "name": "lookup_order",
                            "description": "Look up an existing order by its ID. Use this function when: A customer asks about a specific order. A customer wants to check order status. A customer says 'Where is my order' or 'Check order number'. A customer provides an order ID number. Always verify the order exists and provide clear information about the order details.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "order_id": {
                                        "type": "integer",
                                        "description": "The order ID number to look up. Must be a valid order number in our system."
                                    }
                                },
                                "required": ["order_id"]
                            }
                        }
                    ]
                },
                "speak": {
                    "provider": {
                        "type": "deepgram",
                        "model": "aura-2-thalia-en"
                    }
                },
                "greeting": "Hello! I'm your pharmacy assistant. I can help you with drug information, placing orders, and checking order status. When placing orders, I'll need your full name spelled out clearly and will confirm all details with you. How can I assist you today?"
            }
        })

    async def sts_sender(self):
        """
        Send audio from Twilio to Deepgram.
        Maintains the same logic as the original sts_sender function.
        """
        print("sts_sender started")
        try:
            while True:
                chunk = await self.audio_queue.get()
                if self.sts_ws:
                    await self.sts_ws.send(chunk)
        except Exception as e:
            print(f"Error in sts_sender: {e}")

    async def sts_receiver(self):
        """
        Receive responses from Deepgram and send to Twilio.
        Maintains the same logic as the original sts_receiver function.
        """
        print("sts_receiver started")
        try:
            # Wait for stream ID from Twilio
            streamsid = await self.streamsid_queue.get()

            # Process messages from Deepgram
            async for message in self.sts_ws:
                if isinstance(message, str):
                    print(message)
                    decoded = json.loads(message)
                    await self.handle_text_message(decoded, streamsid)
                    continue

                # Handle binary audio messages
                raw_mulaw = message

                # Construct Twilio media message
                media_message = {
                    "event": "media",
                    "streamSid": streamsid,
                    "media": {"payload": base64.b64encode(raw_mulaw).decode("ascii")}
                }

                # Send audio to Twilio
                await self.send(text_data=json.dumps(media_message))

        except Exception as e:
            print(f"Error in sts_receiver: {e}")

    async def handle_text_message(self, decoded, streamsid):
        """
        Handle text messages from Deepgram.
        Maintains the same logic as the original implementation.
        """
        # Handle barge-in
        await self.handle_barge_in(decoded, streamsid)

        # Handle function call requests
        if decoded["type"] == "FunctionCallRequest":
            await self.handle_function_call_request(decoded)

    async def handle_barge_in(self, decoded, streamsid):
        """
        Handle user interruption (barge-in).
        Maintains the same logic as the original implementation.
        """
        if decoded["type"] == "UserStartedSpeaking":
            clear_message = {
                "event": "clear",
                "streamSid": streamsid
            }
            await self.send(text_data=json.dumps(clear_message))

    async def handle_function_call_request(self, decoded):
        """
        Handle function call requests from Deepgram.
        Maintains the same logic as the original implementation.
        """
        try:
            for function_call in decoded["functions"]:
                func_name = function_call["name"]
                func_id = function_call["id"]
                arguments = json.loads(function_call["arguments"])

                print(f"Function call: {func_name} (ID: {func_id}), arguments: {arguments}")

                # Execute the function
                result = self.execute_function_call(func_name, arguments)

                # Send response back to Deepgram
                function_result = self.create_function_call_response(func_id, func_name, result)
                await self.sts_ws.send(json.dumps(function_result))
                print(f"Sent function result: {function_result}")

        except Exception as e:
            print(f"Error calling function: {e}")
            error_result = self.create_function_call_response(
                func_id if "func_id" in locals() else "unknown",
                func_name if "func_name" in locals() else "unknown",
                {"error": f"Function call failed with: {str(e)}"}
            )
            await self.sts_ws.send(json.dumps(error_result))

    def execute_function_call(self, func_name, arguments):
        """
        Execute pharmacy function calls.
        Maintains the same logic as the original implementation.
        """
        if func_name in FUNCTION_MAP:
            result = FUNCTION_MAP[func_name](**arguments)
            print(f"Function call result: {result}")
            return result
        else:
            result = {"error": f"Unknown function: {func_name}"}
            print(result)
            return result

    def create_function_call_response(self, func_id, func_name, result):
        """
        Create function call response for Deepgram.
        Maintains the same format as the original implementation.
        """
        return {
            "type": "FunctionCallResponse",
            "id": func_id,
            "name": func_name,
            "content": json.dumps(result)
        }
