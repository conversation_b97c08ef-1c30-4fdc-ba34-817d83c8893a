"""
<PERSON><PERSON><PERSON> to populate initial drug data in the Django database.
This replicates the DRUG_DB data from the original pharmacy_functions.py.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepgram_voice_agent.settings')
django.setup()

from pharmacy.models import Drug

def populate_drugs():
    """
    Create initial drug data that matches the original DRUG_DB structure.
    """
    
    # Original DRUG_DB data from pharmacy_functions.py
    drugs_data = [
        {
            "drug_key": "aspirin",
            "name": "Acetylsalicylic Acid",
            "price": 5.99,
            "description": "Non-steroidal anti-inflammatory drug for pain relief and fever reduction",
            "quantity": 30
        },
        {
            "drug_key": "ibuprofen",
            "name": "Ibuprofen",
            "price": 7.99,
            "description": "Anti-inflammatory medication for pain and inflammation management",
            "quantity": 20
        },
        {
            "drug_key": "acetaminophen",
            "name": "Acetaminophen",
            "price": 6.99,
            "description": "Analgesic and antipyretic medication for pain and fever control",
            "quantity": 25
        },
        {
            "drug_key": "metformin",
            "name": "Metformin Hydrochloride",
            "price": 12.50,
            "description": "Biguanide antidiabetic medication for type 2 diabetes management",
            "quantity": 60
        },
        {
            "drug_key": "lisinopril",
            "name": "Lisinopril",
            "price": 8.75,
            "description": "ACE inhibitor for hypertension and heart failure treatment",
            "quantity": 30
        },
        {
            "drug_key": "atorvastatin",
            "name": "Atorvastatin Calcium",
            "price": 15.25,
            "description": "HMG-CoA reductase inhibitor for cholesterol management",
            "quantity": 30
        },
        {
            "drug_key": "omeprazole",
            "name": "Omeprazole",
            "price": 11.99,
            "description": "Proton pump inhibitor for acid reflux and ulcer treatment",
            "quantity": 28
        },
        {
            "drug_key": "amlodipine",
            "name": "Amlodipine Besylate",
            "price": 9.50,
            "description": "Calcium channel blocker for hypertension and angina",
            "quantity": 30
        },
        {
            "drug_key": "metoprolol",
            "name": "Metoprolol Tartrate",
            "price": 7.25,
            "description": "Beta-blocker for hypertension and heart rhythm disorders",
            "quantity": 30
        },
        {
            "drug_key": "sertraline",
            "name": "Sertraline Hydrochloride",
            "price": 13.75,
            "description": "Selective serotonin reuptake inhibitor for depression and anxiety",
            "quantity": 30
        }
    ]

    created_count = 0
    updated_count = 0

    for drug_data in drugs_data:
        drug, created = Drug.objects.get_or_create(
            drug_key=drug_data["drug_key"],
            defaults={
                "name": drug_data["name"],
                "price": drug_data["price"],
                "description": drug_data["description"],
                "quantity": drug_data["quantity"]
            }
        )
        
        if created:
            created_count += 1
            print(f'Created drug: {drug.name}')
        else:
            # Update existing drug with new data
            drug.name = drug_data["name"]
            drug.price = drug_data["price"]
            drug.description = drug_data["description"]
            drug.quantity = drug_data["quantity"]
            drug.save()
            updated_count += 1
            print(f'Updated drug: {drug.name}')

    print(f'Successfully populated drugs: {created_count} created, {updated_count} updated')

if __name__ == '__main__':
    populate_drugs()
