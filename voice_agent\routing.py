"""
WebSocket URL routing for the voice agent app.
This maintains the same endpoint structure as the original main.py implementation.
"""

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Twilio WebSocket endpoint - maintains compatibility with original /twilio path
    re_path(r'ws/twilio/$', consumers.TwilioVoiceConsumer.as_asgi()),
    # Alternative path for backward compatibility
    re_path(r'twilio/$', consumers.TwilioVoiceConsumer.as_asgi()),
]
