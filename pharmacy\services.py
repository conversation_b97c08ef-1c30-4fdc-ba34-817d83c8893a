"""
Pharmacy services that maintain the same interface as the original pharmacy_functions.py
but use Django ORM for data persistence.
"""

from .models import Drug, Order
from django.core.exceptions import ObjectDoesNotExist
from decimal import Decimal


class PharmacyService:
    """Service class for pharmacy operations."""
    
    @staticmethod
    def get_drug_info(drug_name):
        """
        Get drug information.
        Maintains the same interface as the original get_drug_info function.
        
        Args:
            drug_name (str): Name of the drug to look up
            
        Returns:
            dict: Drug information or error message
        """
        try:
            drug = Drug.objects.get(drug_key=drug_name.lower())
            return {
                "name": drug.name,
                "description": drug.description,
                "price": float(drug.price),
                "quantity": drug.quantity
            }
        except ObjectDoesNotExist:
            return {"error": f"Drug '{drug_name}' not found"}
    
    @staticmethod
    def place_order(customer_name, drug_name):
        """
        Place a simple order with predefined quantity.
        Maintains the same interface as the original place_order function.
        
        Args:
            customer_name (str): Customer's full name
            drug_name (str): Name of the drug to order
            
        Returns:
            dict: Order confirmation or error message
        """
        try:
            drug = Drug.objects.get(drug_key=drug_name.lower())
        except ObjectDoesNotExist:
            return {"error": f"Drug '{drug_name}' not found"}
        
        # Create the order
        order = Order.objects.create(
            customer_name=customer_name,
            drug=drug,
            quantity=drug.quantity,
            total_price=drug.price,
            status='pending'
        )
        
        return {
            "order_id": order.id,
            "message": f"Order {order.id} placed: {drug.quantity} {drug.name} for ${float(order.total_price):.2f}",
            "total": float(order.total_price),
            "quantity": drug.quantity
        }
    
    @staticmethod
    def lookup_order(order_id):
        """
        Look up an order.
        Maintains the same interface as the original lookup_order function.
        
        Args:
            order_id (int): Order ID to look up
            
        Returns:
            dict: Order details or error message
        """
        try:
            order = Order.objects.get(id=int(order_id))
            return {
                "order_id": order.id,
                "customer": order.customer_name,
                "drug": order.drug.name,
                "quantity": order.quantity,
                "total": float(order.total_price),
                "status": order.status
            }
        except (ObjectDoesNotExist, ValueError):
            return {"error": f"Order {order_id} not found"}


# Create service instance
pharmacy_service = PharmacyService()

# Function mapping dictionary that maintains compatibility with original code
FUNCTION_MAP = {
    'get_drug_info': pharmacy_service.get_drug_info,
    'place_order': pharmacy_service.place_order,
    'lookup_order': pharmacy_service.lookup_order
}
