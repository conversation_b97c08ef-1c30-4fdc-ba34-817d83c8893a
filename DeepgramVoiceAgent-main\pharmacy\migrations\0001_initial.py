# Generated by Django 5.2.4 on 2025-07-31 13:42

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Drug",
            fields=[
                (
                    "drug_key",
                    models.CharField(
                        help_text="Lowercase drug identifier",
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Full drug name", max_length=100)),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price per unit",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Drug description and usage"),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(help_text="Default quantity per order"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Drug",
                "verbose_name_plural": "Drugs",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Order",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "customer_name",
                    models.CharField(help_text="Customer's full name", max_length=200),
                ),
                ("quantity", models.PositiveIntegerField(help_text="Quantity ordered")),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total order price",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        help_text="Order status",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "drug",
                    models.ForeignKey(
                        help_text="Ordered drug",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="pharmacy.drug",
                    ),
                ),
            ],
            options={
                "verbose_name": "Order",
                "verbose_name_plural": "Orders",
                "ordering": ["-created_at"],
            },
        ),
    ]
