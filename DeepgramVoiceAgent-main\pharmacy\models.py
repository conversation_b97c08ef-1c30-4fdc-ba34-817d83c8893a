from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Drug(models.Model):
    """Model representing a drug in the pharmacy inventory."""

    # Use the lowercase name as the primary key to match original DRUG_DB structure
    drug_key = models.CharField(max_length=50, primary_key=True, help_text="Lowercase drug identifier")
    name = models.CharField(max_length=100, help_text="Full drug name")
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Price per unit"
    )
    description = models.TextField(help_text="Drug description and usage")
    quantity = models.PositiveIntegerField(help_text="Default quantity per order")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Drug"
        verbose_name_plural = "Drugs"

    def __str__(self):
        return f"{self.name} (${self.price})"


class Order(models.Model):
    """Model representing a pharmacy order."""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    # Auto-incrementing ID matches original ORDERS_DB["next_id"] behavior
    id = models.AutoField(primary_key=True)
    customer_name = models.CharField(max_length=200, help_text="Customer's full name")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, help_text="Ordered drug")
    quantity = models.PositiveIntegerField(help_text="Quantity ordered")
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Total order price"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Order status"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Order"
        verbose_name_plural = "Orders"

    def __str__(self):
        return f"Order #{self.id} - {self.customer_name} - {self.drug.name}"

    def save(self, *args, **kwargs):
        """Override save to calculate total price if not provided."""
        if not self.total_price and self.drug:
            self.total_price = self.drug.price * self.quantity
        super().save(*args, **kwargs)
